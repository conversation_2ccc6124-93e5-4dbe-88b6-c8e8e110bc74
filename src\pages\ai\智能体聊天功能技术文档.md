# 智能体聊天功能技术文档

## 概述

本文档基于现有的智能体聊天界面实现，详细说明了如何在新项目中实现基础的智能体聊天功能，包括流式对话和历史记录管理两个核心功能。

## 1. 接口清单

### 1.0 接口域名配置

#### 基础路径
所有接口的基础路径为：`/data/ai-platform-backend/api/v1`

#### 不同环境的完整域名
- **开发环境**：`https://dev.shukeyun.com/data/ai-platform-backend/api/v1`
- **测试环境**：`https://test.shukeyun.com/data/ai-platform-backend/api/v1`
- **生产环境**：`https://prod.shukeyun.com/data/ai-platform-backend/api/v1`

#### 本地开发代理
本地开发时，通过 Vite 代理转发：
```
本地请求：http://localhost:5173/data/ai-platform-backend/api/v1
代理转发到：https://dev.shukeyun.com/data/ai-platform-backend/api/v1
```

### 1.1 智能体配置接口

#### 获取智能体详情
- **接口路径**: `GET /agent/detail/{version_id}`
- **完整地址示例**: `https://dev.shukeyun.com/data/ai-platform-backend/api/v1/agent/detail/123`
- **请求方法**: GET
- **参数**:
  - `version_id`: 智能体ID（路径参数）
  - `noToLogin`: 是否需要登录验证（查询参数）
- **用途**: 获取智能体的完整配置信息

### 1.2 聊天对话接口

#### 流式聊天
- **接口路径**: `POST /agent/chat/{agent_id}`
- **完整地址示例**: `https://dev.shukeyun.com/data/ai-platform-backend/api/v1/agent/chat/123`
- **请求方法**: POST (Server-Sent Events)
- **请求头**:
  ```
  Content-Type: application/json
  Authorization: Bearer {token}
  ```
- **请求体**:
  ```json
  {
    "chat_context_id": "会话上下文ID",
    "history": [
      {"role": "user", "content": "历史消息"},
      {"role": "assistant", "content": "历史回复"}
    ],
    "content": [
      {"type": "text", "text": "当前用户输入"}
    ]
  }
  ```

### 1.3 历史记录接口

#### 获取聊天历史列表
- **接口路径**: `GET /agent/chat/{agent_id}/history/all`
- **完整地址示例**: `https://dev.shukeyun.com/data/ai-platform-backend/api/v1/agent/chat/123/history/all`
- **请求方法**: GET
- **参数**:
  - `agent_id`: 智能体ID
  - `noToLogin`: 是否需要登录验证

#### 删除历史记录
- **接口路径**: `DELETE /agent/chat/history/{history_id}`
- **完整地址示例**: `https://dev.shukeyun.com/data/ai-platform-backend/api/v1/agent/chat/history/456`
- **请求方法**: DELETE
- **参数**:
  - `history_id`: 历史记录ID





## 2. 核心功能实现

### 2.1 智能体初始化

```javascript
// 组件挂载时获取智能体配置
const initializeAgent = async (agentId) => {
  try {
    const agentData = await getAgentDetail(agentId, true);

    // 解析配置
    const { icon_url, name, deploy_id, config } = agentData;
    const {
      opener_prompt,
      suggested_questions,
      background_image,
    } = config;

    // 设置基础状态
    basicState.value = {
      name,
      logo: icon_url,
      deploy_id,
      prologue: opener_prompt.is_enable ? opener_prompt.value : '',
      background: background_image.is_enable ? background_image.value : '',
      suggestedQuestions: suggested_questions.is_enable ? suggested_questions.value : []
    };



  } catch (error) {
    console.error('智能体初始化失败:', error);
    throw error;
  }
};
```

### 2.2 流式聊天实现

```javascript
import { fetchEventSource } from '@microsoft/fetch-event-source';

const sendMessage = async (userInput) => {
  // 1. 添加用户消息到聊天记录
  const userMessage = {
    role: 'user',
    content: userInput
  };
  messages.value.push(userMessage);
  
  // 2. 添加空的助手消息占位
  const assistantMessage = { role: 'assistant', content: '', citations: [] };
  messages.value.push(assistantMessage);
  
  // 3. 准备请求数据
  const requestBody = {
    chat_context_id: currentChatId.value || null,
    history: messages.value.slice(0, -2).map(msg => ({
      role: msg.role,
      content: msg.content
    })),
    content: [
      { type: 'text', text: userInput }
    ]
  };
  
  // 4. 发起流式请求
  let accumulatedText = '';
  const abortController = new AbortController();
  
  try {
    await fetchEventSource(
      `/data/ai-platform-backend/api/v1/agent/chat/${agentId}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify(requestBody),
        signal: abortController.signal,
        
        onopen(response) {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        },
        
        onmessage(event) {
          if (event.data === '[DONE]') return;
          
          try {
            accumulatedText += event.data;
            // 实时更新显示（可选：添加打字机效果）
            updateMessageContent(accumulatedText);
          } catch (error) {
            console.error('解析流式数据失败:', error);
          }
        },
        
        onclose() {
          console.log('流式连接关闭');
        },
        
        onerror(error) {
          console.error('流式请求错误:', error);
          throw error;
        }
      }
    );
    
    // 5. 处理完整响应
    await processCompleteResponse(accumulatedText);
    
  } catch (error) {
    handleChatError(error);
  }
};

// 处理完整响应
const processCompleteResponse = async (fullText) => {
  // 提取引用信息
  const citationRegex = /<citation>(.*?)<\/citation>/g;
  const citationMatch = citationRegex.exec(fullText);
  if (citationMatch && citationMatch[1]) {
    messages.value[messages.value.length - 1].citations = JSON.parse(citationMatch[1]);
  }
  
  // 渲染Markdown并移除citation标签
  const cleanText = fullText.replace(/<citation>[\s\S]*?<\/citation>/g, '');
  const renderedContent = markdownRenderer.render(cleanText);
  
  // 更新最后一条消息
  messages.value[messages.value.length - 1].content = renderedContent;
  
  // 刷新历史记录
  await refreshChatHistory();
};
```

### 2.3 历史记录管理

```javascript
// 获取聊天历史
const loadChatHistory = async (agentId) => {
  try {
    const historyList = await getChatHistoryList(agentId, true);
    return historyList.map(item => ({
      id: item.id,
      title: extractFirstMessage(item.history),
      updatedAt: item.updated_at,
      history: parseHistoryData(item.history)
    }));
  } catch (error) {
    console.error('获取历史记录失败:', error);
    return [];
  }
};

// 解析历史数据
const parseHistoryData = (historyData) => {
  if (typeof historyData === 'string') {
    const jsonStr = historyData.replace(/'/g, '"');
    return JSON.parse(jsonStr);
  }
  return historyData;
};

// 提取首条消息作为标题
const extractFirstMessage = (historyData) => {
  try {
    const parsedHistory = parseHistoryData(historyData);
    const firstMessage = parsedHistory[0];

    if (Array.isArray(firstMessage.content)) {
      return firstMessage.content[0]?.text || firstMessage.content[0] || '';
    }
    return firstMessage.content || '';
  } catch (error) {
    return '解析错误';
  }
};

// 加载历史会话
const loadHistoryChat = (historyData, chatId) => {
  currentChatId.value = chatId;
  messages.value = [];

  const parsedHistory = parseHistoryData(historyData);
  const formattedMessages = parsedHistory.map(item => ({
    role: item.role,
    content: Array.isArray(item.content) ? item.content[0].text : item.content,
    citations: item.citations || []
  }));

  messages.value.push(...formattedMessages);
};

// 删除历史记录
const deleteHistoryChat = async (historyId) => {
  try {
    await delChatData(historyId);
    await loadChatHistory(agentId); // 重新加载列表

    // 如果删除的是当前会话，清空聊天区域
    if (currentChatId.value === historyId) {
      messages.value = [];
      currentChatId.value = '';
    }
  } catch (error) {
    console.error('删除历史记录失败:', error);
    throw error;
  }
};
```



## 3. 数据结构定义

### 3.1 智能体配置数据结构

```typescript
interface AgentConfig {
  icon_url: string;
  name: string;
  deploy_id: string;
  config: {
    opener_prompt: {
      value: string;
      is_enable: boolean;
    };
    suggested_questions: {
      value: string[];
      is_enable: boolean;
    };
    background_image: {
      value: string;
      is_enable: boolean;
    };
  };
}
```

### 3.2 消息数据结构

```typescript
interface Message {
  role: 'user' | 'assistant';
  content: string;
}
```

### 3.3 历史记录数据结构

```typescript
interface ChatHistory {
  id: string;
  title: string; // 从第一条消息提取
  updated_at: string;
  history: Message[];
}
```



## 4. 状态管理

### 4.1 核心状态变量

```javascript
// 智能体基础信息
const basicState = reactive({
  name: '',
  logo: '',
  deploy_id: '',
  prologue: '',
  background: '',
  suggestedQuestions: []
});

// 聊天相关状态
const messages = ref([]); // 当前会话消息列表
const currentChatId = ref(''); // 当前会话ID
const isLoading = ref(false); // 是否正在加载回复
const inputMessage = ref(''); // 用户输入内容

// 历史记录状态
const historyList = ref([]); // 历史会话列表
const currentSelectedId = ref(''); // 当前选中的历史会话ID

// 控制状态
const abortController = ref(null); // 用于中断流式请求
```

### 4.2 数据流说明

1. **初始化流程**：
   ```
   组件挂载 → 获取智能体配置 → 设置基础状态 → 加载历史记录
   ```

2. **发送消息流程**：
   ```
   用户输入 → 添加用户消息 → 发起流式请求 → 实时更新AI回复 → 获取追问建议 → 刷新历史记录
   ```

3. **历史记录流程**：
   ```
   点击历史会话 → 解析历史数据 → 重置当前会话 → 更新消息列表
   ```



## 5. 错误处理机制

### 5.1 网络请求错误处理

```javascript
// 统一的错误处理函数
const handleApiError = (error, context = '') => {
  console.error(`${context}失败:`, error);

  if (error.name === 'AbortError') {
    // 用户主动取消请求
    return;
  }

  if (error.response) {
    // 服务器返回错误状态码
    const { status, data } = error.response;
    switch (status) {
      case 401:
        message.error('登录已过期，请重新登录');
        // 跳转到登录页面
        break;
      case 403:
        message.error('没有权限访问该功能');
        break;
      case 404:
        message.error('请求的资源不存在');
        break;
      case 500:
        message.error('服务器内部错误，请稍后重试');
        break;
      default:
        message.error(data?.message || '请求失败，请稍后重试');
    }
  } else if (error.request) {
    // 网络连接错误
    message.error('网络连接失败，请检查网络设置');
  } else {
    // 其他错误
    message.error(error.message || '发生未知错误');
  }
};

// 流式聊天错误处理
const handleChatError = (error) => {
  isLoading.value = false;

  if (messages.value.length > 0) {
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage.role === 'assistant' && !lastMessage.content) {
      lastMessage.content = '抱歉，服务暂时不可用，请稍后重试';
    }
  }

  handleApiError(error, '发送消息');
};
```

### 5.2 数据解析错误处理

```javascript
// 安全的JSON解析
const safeJsonParse = (jsonString, fallback = null) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('JSON解析失败:', error);
    return fallback;
  }
};

// 安全的历史数据解析
const safeParseHistoryData = (historyData) => {
  try {
    if (typeof historyData === 'string') {
      const jsonStr = historyData.replace(/'/g, '"');
      return JSON.parse(jsonStr);
    }
    return historyData;
  } catch (error) {
    console.error('历史数据解析失败:', error);
    return [];
  }
};
```

### 5.3 组件卸载时的清理

```javascript
// 组件卸载时清理资源
onUnmounted(() => {
  // 中断正在进行的请求
  if (abortController.value) {
    abortController.value.abort();
  }

  // 清理定时器
  if (typingInterval.value) {
    clearTimeout(typingInterval.value);
  }


});
```

## 6. 实现步骤（按优先级排序）

### 6.1 第一阶段：基础聊天功能（高优先级）

#### 步骤1：环境准备
1. 安装必要依赖：
   ```bash
   npm install @microsoft/fetch-event-source markdown-it
   ```

2. 配置请求拦截器和认证：
   ```javascript
   // utils/request.js
   import axios from 'axios';

   const request = axios.create({
     baseURL: '/data/ai-platform-backend/api/v1',  // 注意：使用完整的基础路径
     timeout: 30000
   });

   request.interceptors.request.use(config => {
     const token = localStorage.getItem('auth_token');
     if (token) {
       config.headers.Authorization = `Bearer ${token}`;
     }
     return config;
   });
   ```

#### 步骤2：实现智能体配置获取
1. 创建API接口函数：
   ```javascript
   // api/agent.js
   export const getAgentDetail = (agentId, noToLogin = false) => {
     return request.get(`/agent/detail/${agentId}`, { params: { noToLogin } });
   };
   ```

2. 实现组件初始化逻辑

#### 步骤3：实现基础流式聊天
1. 集成Server-Sent Events
2. 实现消息发送和接收
3. 添加Markdown渲染支持

#### 步骤4：基础UI交互
1. 消息列表显示
2. 输入框和发送按钮
3. 加载状态显示

### 6.2 第二阶段：历史记录功能（中优先级）

#### 步骤5：历史记录管理
1. 实现历史记录获取接口
2. 添加历史记录列表UI
3. 实现历史会话加载功能
4. 添加删除历史记录功能

#### 步骤6：会话状态管理
1. 实现会话ID管理
2. 添加会话切换逻辑
3. 优化数据持久化

### 6.3 第三阶段：UI优化（低优先级）

#### 步骤7：UI优化
1. 添加打字机效果
2. 优化滚动定位
3. 添加主题和样式定制

## 7. 关键技术要点

### 7.1 Server-Sent Events最佳实践

```javascript
// 推荐的SSE实现模式
const createSSEConnection = async (url, options) => {
  const abortController = new AbortController();

  try {
    await fetchEventSource(url, {
      ...options,
      signal: abortController.signal,

      // 连接重试配置
      openWhenHidden: true,

      // 错误重试逻辑
      onopen: async (response) => {
        if (response.ok && response.headers.get('content-type')?.includes('text/stream')) {
          return; // 连接成功
        } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
          throw new FatalError(); // 客户端错误，不重试
        } else {
          throw new RetriableError(); // 服务器错误，可重试
        }
      },

      onmessage: (event) => {
        // 处理消息
      },

      onerror: (error) => {
        // 错误处理
        throw error;
      }
    });
  } catch (error) {
    if (error.name !== 'AbortError') {
      console.error('SSE连接失败:', error);
    }
  }

  return abortController;
};
```

### 7.2 性能优化建议

1. **消息列表虚拟滚动**：当消息数量较多时，使用虚拟滚动优化性能
2. **防抖输入**：对用户输入进行防抖处理，避免频繁触发
3. **内存管理**：及时清理不需要的消息历史，避免内存泄漏

### 7.3 安全考虑

1. **输入验证**：对用户输入进行XSS防护
2. **认证管理**：实现token刷新机制
3. **内容过滤**：对AI回复内容进行安全检查

## 8. 测试建议

### 8.1 单元测试
- API接口调用测试
- 数据解析函数测试
- 状态管理逻辑测试

### 8.2 集成测试
- 完整聊天流程测试
- 历史记录功能测试
- 错误处理场景测试

### 8.3 用户体验测试
- 流式响应体验测试
- 多设备兼容性测试
- 网络异常情况测试

## 9. 部署注意事项

1. **代理配置**：确保SSE请求正确代理到后端
2. **超时设置**：合理设置请求超时时间
3. **缓存策略**：配置合适的静态资源缓存
4. **监控告警**：添加接口调用监控和错误告警

## 10. 扩展功能建议

1. **消息搜索**：在历史记录中搜索特定内容
2. **导出功能**：支持导出聊天记录
3. **快捷回复**：预设常用回复模板
4. **多语言支持**：国际化界面文本
5. **主题定制**：支持深色模式和主题切换

## 11. 核心接口清单总结

### 完整接口地址（以开发环境为例）

1. **获取智能体基础信息**
   ```
   GET https://dev.shukeyun.com/data/ai-platform-backend/api/v1/agent/detail/{version_id}
   ```

2. **流式聊天**
   ```
   POST https://dev.shukeyun.com/data/ai-platform-backend/api/v1/agent/chat/{agent_id}
   ```

3. **获取历史记录**
   ```
   GET https://dev.shukeyun.com/data/ai-platform-backend/api/v1/agent/chat/{agent_id}/history/all
   ```

4. **删除历史记录**
   ```
   DELETE https://dev.shukeyun.com/data/ai-platform-backend/api/v1/agent/chat/history/{history_id}
   ```

### 环境切换
- 开发环境：将域名替换为 `https://dev.shukeyun.com`
- 测试环境：将域名替换为 `https://test.shukeyun.com`
- 生产环境：将域名替换为 `https://prod.shukeyun.com`

---

通过以上技术文档，开发者可以快速实现基础的智能体聊天功能。建议按照优先级逐步实现，先确保核心聊天功能稳定运行，再添加历史记录管理功能。
